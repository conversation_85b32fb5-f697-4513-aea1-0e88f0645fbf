
----------
Loaded log file: "server_log.txt".
----------

SA-MP Dedicated Server
----------------------
v0.3.7-R2, (C)2005-2015 SA-MP Team

[02:31:24] language = ""  (string)
[02:31:24] 
[02:31:24] Server Plugins
[02:31:24] --------------
[02:31:24]  Loading plugin: crashdetect
[02:31:24]   CrashDetect plugin 4.21
[02:31:24]   Loaded.
[02:31:24]  Loading plugin: sampvoice
[02:31:24] [sv:dbg:network:init] : module initializing...
[02:31:24] [dbg:raknet:init] : module initializing...
[02:31:24] [dbg:raknet:init] : module initialized
[02:31:24] [sv:dbg:network:init] : module initialized
[02:31:24] [sv:dbg:main:Load] : creating 2 work threads...
[02:31:24]  -------------------------------------------    
[02:31:24]    ___                __   __    _              
[02:31:24]   / __| __ _ _ __  _ _\ \ / /__ (_) __ ___    
[02:31:24]   \__ \/ _` | '  \| '_ \   / _ \| |/ _/ -_)
[02:31:24]   |___/\__,_|_|_|_| .__/\_/\___/|_|\__\___|
[02:31:25]                   |_|                           
[02:31:25]  -------------------------------------------    
[02:31:25]            SampVoice by MOR loaded              
[02:31:25]         Compiled By SAMPINDO Community          
[02:31:25]  -------------------------------------------    
[02:31:25]   Loaded.
[02:31:26]  Loading plugin: KeyListener
[02:31:27] KeyListener v1.1.2 by MOR loaded
[02:31:27]   Loaded.
[02:31:27]  Loading plugin: SKY
[02:31:28] 
[02:31:28]  ===============================
[02:31:28]    
[02:31:28]    < SKY - 2.3.0 >
[02:31:29]    
[02:31:29]    (c) 2008 - Present | YSF Maintainers
[02:31:29]    (c) 2015 | Oscar "Slice" Broman
[02:31:30]    
[02:31:30]    Server Version: 0.3.7 R2
[02:31:30]    Operating System: Windows
[02:31:31]    Built on: Jan 21 2025 at 03:38:30
[02:31:31]    
[02:31:31]  ===============================
[02:31:32] 
[02:31:32]   Loaded.
[02:31:32]  Loading plugin: mysql
[02:31:33]  >> plugin.mysql: R41-4 successfully loaded.
[02:31:33]   Loaded.
[02:31:34]  Loading plugin: streamer
[02:31:34] 

*** Streamer Plugin v2.9.5 by Incognito loaded ***

[02:31:34]   Loaded.
[02:31:35]  Loading plugin: sscanf
[02:31:35] 
[02:31:35]  ===============================
[02:31:36] 
[02:31:36]       sscanf plugin loaded.     
[02:31:37] 
[02:31:37]          Version: 2.13.8
[02:31:38] 
[02:31:38]    (c) 2022 Alex "Y_Less" Cole  
[02:31:39] 
[02:31:39]  ===============================
[02:31:39] 
[02:31:40]   Loaded.
[02:31:40]  Loading plugin: samp_bcrypt
[02:31:41] [SampBcrypt] [info]: Version: 0.3.4
[02:31:42]   Loaded.
[02:31:42]  Loading plugin: textdraw-streamer
[02:31:42] 
[02:31:43]  ===============================
[02:31:43]                                 
[02:31:43]     Textdraw Streamer Yuklendi  
[02:31:44]                                 
[02:31:44]           Surum: v1.6.2            
[02:31:44]                                 
[02:31:44]     Developer: Burak (NexoR)    
[02:31:45]                                 
[02:31:45]  ===============================
[02:31:45] 
[02:31:46]   Loaded.
[02:31:46]  Loaded 9 plugins.

[02:31:47] 
[02:31:48] Filterscripts
[02:31:48] ---------------
[02:31:48]   Loading filterscript 'mapping.amx'...
[02:31:48] [sv:dbg:main:AmxLoad] : net game pointer (value:026C9510) received
[02:31:49] [sv:dbg:network:bind] : voice server running on port 12148
[02:31:49]  
[02:31:49]  
[02:31:49]          ==============================================================
[02:31:49]          |                                                            |
[02:31:49]          |                                                            |
[02:31:49]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[02:31:49]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[02:31:49]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[02:31:49]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[02:31:49]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[02:31:49]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[02:31:49]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[02:31:49]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[02:31:49]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[02:31:49]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[02:31:49]          |           Y:::::Y                    S:::::S   I::::I      |
[02:31:49]          |           Y:::::Y                    S:::::S   I::::I      |
[02:31:49]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[02:31:49]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[02:31:49]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[02:31:49]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[02:31:49]          |                                                            |
[02:31:49]          |                                                            |
[02:31:49]          |                      (c) 2021 MPL v1.1                     |
[02:31:49]          |            Alex "Y_Less" Cole and contributors.            |
[02:31:49]          |                                                            |
[02:31:49]          |                                                            |
[02:31:49]          ==============================================================
[02:31:49]  
[02:31:49]  
[02:31:49]  ========================================== 
[02:31:49]  |                                        | 
[02:31:49]  |   Generating code, this may take a     | 
[02:31:49]  |  little bit of time.  Note that this   | 
[02:31:49]  |  code generation works best with the   | 
[02:31:49]  |     JIT plugin, which you are not      | 
[02:31:49]  |     currently using.  Get it here:     | 
[02:31:49]  |                                        | 
[02:31:49]  |       https://git.io/jit-plugin        | 
[02:31:49]  |                                        | 
[02:31:49]  |             Please wait...             | 
[02:31:49]  |                                        | 
[02:31:49]  |             Done in 0ms!               | 
[02:31:49]  |                                        | 
[02:31:49]  ========================================== 
[02:31:49]  |                                        | 
[02:31:49]  | Server:      0.3.7-R2 (W)              | 
[02:31:49]  | Started:     04 Jun 2025 - 02:31:49    | 
[02:31:49]  |                                        | 
[02:31:49]  | Compiler:    3.10.10 (Windows)         | 
[02:31:49]  | Includes:    0.3DL                     | 
[02:31:49]  | Codepage:    <none>                    | 
[02:31:49]  | Built:       02 Feb 2025 - 14:42:04    | 
[02:31:49]  |                                        | 
[02:31:49]  | YSI:         v05.10.0006               | 
[02:31:49]  | Master:      -1                        | 
[02:31:49]  |                                        | 
[02:31:49]  | JIT:         <none>                    | 
[02:31:49]  | Crashdetect: <found>                   | 
[02:31:49]  |                                        | 
[02:31:49]  ========================================== 
[02:31:49]  
[02:31:49]  
[02:31:49]   Loading filterscript 'testvoice.amx'...
[02:31:49] -------------------- [Arivena Voice Chat System] --------------------
===>> Berhasil dimuat dengan baik!
===>> by: mhyunata (M Wahyu Dinarta)
===>> dipersembahkan untuk Arivena
-------------------------------------------------------------------
[02:31:49]   Loading filterscript 'hotkeys.amx'...
[02:31:49] -------------------- [Arivena Hotkeys System] --------------------
===>> Berhasil dimuat dengan baik!
===>> by: mhyunata (M Wahyu Dinarta)
===>> dipersembahkan untuk Arivena
-------------------------------------------------------------------
[02:31:49]   Loaded 3 filterscripts.

[02:31:50]  
[02:31:50]  
[02:31:50]          ==============================================================
[02:31:50]          |                                                            |
[02:31:50]          |                                                            |
[02:31:50]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[02:31:50]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[02:31:50]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[02:31:50]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[02:31:50]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[02:31:50]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[02:31:50]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[02:31:50]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[02:31:50]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[02:31:50]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[02:31:50]          |           Y:::::Y                    S:::::S   I::::I      |
[02:31:50]          |           Y:::::Y                    S:::::S   I::::I      |
[02:31:50]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[02:31:50]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[02:31:50]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[02:31:50]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[02:31:50]          |                                                            |
[02:31:50]          |                                                            |
[02:31:50]          |                      (c) 2021 MPL v1.1                     |
[02:31:50]          |            Alex "Y_Less" Cole and contributors.            |
[02:31:50]          |                                                            |
[02:31:50]          |                                                            |
[02:31:50]          ==============================================================
[02:31:50]  
[02:31:50]  
[02:31:50]  ========================================== 
[02:31:50]  |                                        | 
[02:31:50]  |   Generating code, this may take a     | 
[02:31:50]  |  little bit of time.  Note that this   | 
[02:31:50]  |  code generation works best with the   | 
[02:31:50]  |     JIT plugin, which you are not      | 
[02:31:50]  |     currently using.  Get it here:     | 
[02:31:50]  |                                        | 
[02:31:50]  |       https://git.io/jit-plugin        | 
[02:31:50]  |                                        | 
[02:31:50]  |             Please wait...             | 
[02:31:53]  |                                        | 
[02:31:53]  |             Done in 2846ms!            | 
[02:31:53]  |                                        | 
[02:31:53]  ========================================== 
[02:31:53]   Filterscript '../scriptfiles/callbackfix.amx' loaded.
[02:31:53] *** YSI Info: Script ID: 1
[02:31:53]  |                                        | 
[02:31:53]  | Server:      0.3.7-R2 (W)              | 
[02:31:53]  | Started:     04 Jun 2025 - 02:31:50    | 
[02:31:53]  |                                        | 
[02:31:53]  | Compiler:    3.10.08 (Windows)         | 
[02:31:53]  | Includes:    0.3DL                     | 
[02:31:53]  | Codepage:    <none>                    | 
[02:31:53]  | Built:       04 Jun 2025 - 02:29:59    | 
[02:31:53]  |                                        | 
[02:31:53]  | YSI:         v05.10.0006               | 
[02:31:53]  | Master:      1                         | 
[02:31:53]  |                                        | 
[02:31:53]  | JIT:         <none>                    | 
[02:31:53]  | Crashdetect: <found>                   | 
[02:31:53]  |                                        | 
[02:31:53]  ========================================== 
[02:31:53]  
[02:31:53]  
[02:31:53] [ADM] Info: Load EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[02:31:53] [MySQL] Koneksi ke database berhasil!
[02:31:53] [Anti-DDoS SA-MP] Telah berhasil dimuat dengan lancar.
[02:31:53] |----------------------------------|
[02:31:53] | Arivena Premiere | A4-RC-Version |
[02:31:53] |----------------------------------|
[02:31:53] Number of vehicle models: 4
[02:31:53] [Dynamic Doors] Jumlah total Doors yang dimuat: 43.
[02:31:54] [Dynamic Garbages] Jumlah total Garbages Bin yang dimuat: 497.
[02:31:54] [Dynamic Shops] Jumlah total Shops yang dimuat: 43.
[02:31:54] [Dynamic Fivem Labels] The total number of Fivem Labels loaded: 1.
[02:31:54] [Dynamic Public Garages] Jumlah total Public Garages yang dimuat: 33.
[02:31:54] [Dynamic Rentals] Total number of loaded Rentals: 6.
[02:31:54] [Dynamic Actors] Jumlah total Actors yang dimuat: 99.
[02:31:54] [Dynamic ATM] Jumlah total ATM yang dimuat: 55.
[02:31:54] [Dynamic Bank Point] Jumlah total bank point yang dimuat: 8.
[02:31:54] [Dynamic Vending] Total dynamic vending loaded: 50.
[02:31:54] [Dynamic Robberies] Jumlah total Robberies yang dimuat: 21.
[02:31:54] [Dynamic Map Icons] Jumlah total map icons yang dimuat: 49.
[02:31:54] [Dynamic Rusun] Total number of rusun loaded: 72.
[02:31:54] [Dynamic Gudang] Total number of gudang loaded: 3.
[02:31:54] [Dynamic Basement] Jumlah total basement yang dimuat: 1.
[02:31:54] [Dynamic Buttons] Jumlah total buttons yang dimuat: 22.
[02:31:54] [Dynamic Kanabis] Jumlah total Kanabis yang dimuat: 23.
[02:31:54] [Dynamic Deers] Jumlah total Deers yang dimuat: 100.
[02:31:54] [Dynamic Families] Jumlah total Family yang dimuat: 1.
[02:31:54] [Dynamic Locker] Jumlah total Locker yang dimuat: 11.
[02:31:54] [Dynamic Fact Craft] Jumlah total Fact Craft yang dimuat: 1.
[02:31:54] [Dynamic Armoury] Jumlah total Armoury yang dimuat: 3.
[02:31:54] [Dynamic Faction Vault] Jumlah total Faction Vault yang dimuat: 11.
[02:31:54] [Dynamic Faction Garage] Jumlah total Faction Garage yang dimuat: 11.
[02:31:54] [Dynamic Roadsigns] Total dynamic road sign loaded: 747.
[02:31:54] [Dynamic Speed Cam] Total dynamic speed cam loaded: 127.
[02:31:54] [Dynamic Graffities] Total Graffiti Tags loaded: 3.
[02:31:54] [Dynamic Blackjack] Jumlah total Blackjack Table yang dimuat: 18.
[02:31:54] [Stuffs] Server Stuff berhasil dimuat...
[02:31:54] [Farm Plants] Jumlah total farm plants yang dimuat: 1.
[02:32:42]  -------------------------------------------
[02:32:42]            SampVoice unloading...           
[02:32:42]  -------------------------------------------
[02:32:42] [dbg:raknet:free] : module releasing...
[02:32:42] [dbg:raknet:free] : module released
[02:32:42] [sv:dbg:network:free] : module releasing...
[02:32:42] [sv:dbg:network:free] : module released

----------
Loaded log file: "server_log.txt".
----------

SA-MP Dedicated Server
----------------------
v0.3.7-R2, (C)2005-2015 SA-MP Team

[02:37:50] language = ""  (string)
[02:37:50] 
[02:37:50] Server Plugins
[02:37:50] --------------
[02:37:50]  Loading plugin: crashdetect
[02:37:50]   CrashDetect plugin 4.21
[02:37:50]   Loaded.
[02:37:50]  Loading plugin: sampvoice
[02:37:50] [sv:dbg:network:init] : module initializing...
[02:37:50] [dbg:raknet:init] : module initializing...
[02:37:50] [dbg:raknet:init] : module initialized
[02:37:50] [sv:dbg:network:init] : module initialized
[02:37:50] [sv:dbg:main:Load] : creating 2 work threads...
[02:37:50]  -------------------------------------------    
[02:37:50]    ___                __   __    _              
[02:37:50]   / __| __ _ _ __  _ _\ \ / /__ (_) __ ___    
[02:37:50]   \__ \/ _` | '  \| '_ \   / _ \| |/ _/ -_)
[02:37:50]   |___/\__,_|_|_|_| .__/\_/\___/|_|\__\___|
[02:37:50]                   |_|                           
[02:37:50]  -------------------------------------------    
[02:37:50]            SampVoice by MOR loaded              
[02:37:50]         Compiled By SAMPINDO Community          
[02:37:50]  -------------------------------------------    
[02:37:50]   Loaded.
[02:37:51]  Loading plugin: KeyListener
[02:37:51] KeyListener v1.1.2 by MOR loaded
[02:37:51]   Loaded.
[02:37:51]  Loading plugin: SKY
[02:37:51] 
[02:37:51]  ===============================
[02:37:51]    
[02:37:51]    < SKY - 2.3.0 >
[02:37:52]    
[02:37:52]    (c) 2008 - Present | YSF Maintainers
[02:37:52]    (c) 2015 | Oscar "Slice" Broman
[02:37:52]    
[02:37:52]    Server Version: 0.3.7 R2
[02:37:52]    Operating System: Windows
[02:37:52]    Built on: Jan 21 2025 at 03:38:30
[02:37:53]    
[02:37:53]  ===============================
[02:37:53] 
[02:37:53]   Loaded.
[02:37:53]  Loading plugin: mysql
[02:37:53]  >> plugin.mysql: R41-4 successfully loaded.
[02:37:54]   Loaded.
[02:37:54]  Loading plugin: streamer
[02:37:54] 

*** Streamer Plugin v2.9.5 by Incognito loaded ***

[02:37:55]   Loaded.
[02:37:55]  Loading plugin: sscanf
[02:37:55] 
[02:37:55]  ===============================
[02:37:56] 
[02:37:56]       sscanf plugin loaded.     
[02:37:56] 
[02:37:56]          Version: 2.13.8
[02:37:57] 
[02:37:57]    (c) 2022 Alex "Y_Less" Cole  
[02:37:57] 
[02:37:57]  ===============================
[02:37:58] 
[02:37:58]   Loaded.
[02:37:58]  Loading plugin: samp_bcrypt
[02:37:58] [SampBcrypt] [info]: Version: 0.3.4
[02:37:59]   Loaded.
[02:37:59]  Loading plugin: textdraw-streamer
[02:37:59] 
[02:37:59]  ===============================
[02:37:59]                                 
[02:38:00]     Textdraw Streamer Yuklendi  
[02:38:00]                                 
[02:38:00]           Surum: v1.6.2            
[02:38:00]                                 
[02:38:00]     Developer: Burak (NexoR)    
[02:38:00]                                 
[02:38:00]  ===============================
[02:38:01] 
[02:38:01]   Loaded.
[02:38:01]  Loaded 9 plugins.

[02:38:02] 
[02:38:02] Filterscripts
[02:38:02] ---------------
[02:38:02]   Loading filterscript 'mapping.amx'...
[02:38:03] [sv:dbg:main:AmxLoad] : net game pointer (value:023994B8) received
[02:38:03] [sv:dbg:network:bind] : voice server running on port 12148
[02:38:03]  
[02:38:03]  
[02:38:03]          ==============================================================
[02:38:03]          |                                                            |
[02:38:03]          |                                                            |
[02:38:03]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[02:38:03]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[02:38:03]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[02:38:03]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[02:38:03]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[02:38:03]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[02:38:03]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[02:38:03]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[02:38:03]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[02:38:03]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[02:38:03]          |           Y:::::Y                    S:::::S   I::::I      |
[02:38:03]          |           Y:::::Y                    S:::::S   I::::I      |
[02:38:03]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[02:38:03]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[02:38:03]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[02:38:03]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[02:38:03]          |                                                            |
[02:38:03]          |                                                            |
[02:38:03]          |                      (c) 2021 MPL v1.1                     |
[02:38:03]          |            Alex "Y_Less" Cole and contributors.            |
[02:38:03]          |                                                            |
[02:38:03]          |                                                            |
[02:38:03]          ==============================================================
[02:38:03]  
[02:38:03]  
[02:38:03]  ========================================== 
[02:38:03]  |                                        | 
[02:38:03]  |   Generating code, this may take a     | 
[02:38:03]  |  little bit of time.  Note that this   | 
[02:38:03]  |  code generation works best with the   | 
[02:38:03]  |     JIT plugin, which you are not      | 
[02:38:03]  |     currently using.  Get it here:     | 
[02:38:03]  |                                        | 
[02:38:03]  |       https://git.io/jit-plugin        | 
[02:38:03]  |                                        | 
[02:38:03]  |             Please wait...             | 
[02:38:03]  |                                        | 
[02:38:03]  |             Done in 0ms!               | 
[02:38:03]  |                                        | 
[02:38:03]  ========================================== 
[02:38:03]  |                                        | 
[02:38:03]  | Server:      0.3.7-R2 (W)              | 
[02:38:03]  | Started:     04 Jun 2025 - 02:38:03    | 
[02:38:03]  |                                        | 
[02:38:03]  | Compiler:    3.10.10 (Windows)         | 
[02:38:03]  | Includes:    0.3DL                     | 
[02:38:03]  | Codepage:    <none>                    | 
[02:38:03]  | Built:       02 Feb 2025 - 14:42:04    | 
[02:38:03]  |                                        | 
[02:38:03]  | YSI:         v05.10.0006               | 
[02:38:03]  | Master:      -1                        | 
[02:38:03]  |                                        | 
[02:38:03]  | JIT:         <none>                    | 
[02:38:03]  | Crashdetect: <found>                   | 
[02:38:03]  |                                        | 
[02:38:03]  ========================================== 
[02:38:03]  
[02:38:03]  
[02:38:03]   Loading filterscript 'testvoice.amx'...
[02:38:03] -------------------- [Arivena Voice Chat System] --------------------
===>> Berhasil dimuat dengan baik!
===>> by: mhyunata (M Wahyu Dinarta)
===>> dipersembahkan untuk Arivena
-------------------------------------------------------------------
[02:38:03]   Loading filterscript 'hotkeys.amx'...
[02:38:03] -------------------- [Arivena Hotkeys System] --------------------
===>> Berhasil dimuat dengan baik!
===>> by: mhyunata (M Wahyu Dinarta)
===>> dipersembahkan untuk Arivena
-------------------------------------------------------------------
[02:38:03]   Loaded 3 filterscripts.

[02:38:04]  
[02:38:04]  
[02:38:04]          ==============================================================
[02:38:04]          |                                                            |
[02:38:04]          |                                                            |
[02:38:04]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[02:38:04]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[02:38:04]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[02:38:04]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[02:38:04]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[02:38:04]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[02:38:04]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[02:38:04]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[02:38:04]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[02:38:04]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[02:38:04]          |           Y:::::Y                    S:::::S   I::::I      |
[02:38:04]          |           Y:::::Y                    S:::::S   I::::I      |
[02:38:04]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[02:38:04]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[02:38:04]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[02:38:04]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[02:38:04]          |                                                            |
[02:38:04]          |                                                            |
[02:38:04]          |                      (c) 2021 MPL v1.1                     |
[02:38:04]          |            Alex "Y_Less" Cole and contributors.            |
[02:38:04]          |                                                            |
[02:38:04]          |                                                            |
[02:38:04]          ==============================================================
[02:38:04]  
[02:38:04]  
[02:38:04]  ========================================== 
[02:38:04]  |                                        | 
[02:38:04]  |   Generating code, this may take a     | 
[02:38:04]  |  little bit of time.  Note that this   | 
[02:38:04]  |  code generation works best with the   | 
[02:38:04]  |     JIT plugin, which you are not      | 
[02:38:04]  |     currently using.  Get it here:     | 
[02:38:04]  |                                        | 
[02:38:04]  |       https://git.io/jit-plugin        | 
[02:38:04]  |                                        | 
[02:38:04]  |             Please wait...             | 
[02:38:07]  |                                        | 
[02:38:07]  |             Done in 2848ms!            | 
[02:38:07]  |                                        | 
[02:38:07]  ========================================== 
[02:38:07]   Filterscript '../scriptfiles/callbackfix.amx' loaded.
[02:38:07] *** YSI Info: Script ID: 1
[02:38:07]  |                                        | 
[02:38:07]  | Server:      0.3.7-R2 (W)              | 
[02:38:07]  | Started:     04 Jun 2025 - 02:38:04    | 
[02:38:07]  |                                        | 
[02:38:07]  | Compiler:    3.10.08 (Windows)         | 
[02:38:07]  | Includes:    0.3DL                     | 
[02:38:07]  | Codepage:    <none>                    | 
[02:38:07]  | Built:       04 Jun 2025 - 02:37:04    | 
[02:38:07]  |                                        | 
[02:38:07]  | YSI:         v05.10.0006               | 
[02:38:07]  | Master:      1                         | 
[02:38:07]  |                                        | 
[02:38:07]  | JIT:         <none>                    | 
[02:38:07]  | Crashdetect: <found>                   | 
[02:38:07]  |                                        | 
[02:38:07]  ========================================== 
[02:38:07]  
[02:38:07]  
[02:38:07] [ADM] Info: Load EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[02:38:07] [MySQL] Koneksi ke database berhasil!
[02:38:07] [Anti-DDoS SA-MP] Telah berhasil dimuat dengan lancar.
[02:38:07] |----------------------------------|
[02:38:07] | Arivena Premiere | A4-RC-Version |
[02:38:07] |----------------------------------|
[02:38:07] Number of vehicle models: 4
[02:38:08] [Dynamic Doors] Jumlah total Doors yang dimuat: 43.
[02:38:08] [Dynamic Garbages] Jumlah total Garbages Bin yang dimuat: 497.
[02:38:08] [Dynamic Shops] Jumlah total Shops yang dimuat: 43.
[02:38:08] [Dynamic Fivem Labels] The total number of Fivem Labels loaded: 1.
[02:38:08] [Dynamic Public Garages] Jumlah total Public Garages yang dimuat: 33.
[02:38:08] [Dynamic Rentals] Total number of loaded Rentals: 6.
[02:38:08] [Dynamic Actors] Jumlah total Actors yang dimuat: 99.
[02:38:08] [Dynamic ATM] Jumlah total ATM yang dimuat: 55.
[02:38:08] [Dynamic Bank Point] Jumlah total bank point yang dimuat: 8.
[02:38:08] [Dynamic Vending] Total dynamic vending loaded: 50.
[02:38:08] [Dynamic Robberies] Jumlah total Robberies yang dimuat: 21.
[02:38:08] [Dynamic Map Icons] Jumlah total map icons yang dimuat: 49.
[02:38:08] [Dynamic Rusun] Total number of rusun loaded: 72.
[02:38:08] [Dynamic Gudang] Total number of gudang loaded: 3.
[02:38:08] [Dynamic Basement] Jumlah total basement yang dimuat: 1.
[02:38:08] [Dynamic Buttons] Jumlah total buttons yang dimuat: 22.
[02:38:08] [Dynamic Kanabis] Jumlah total Kanabis yang dimuat: 23.
[02:38:08] [Dynamic Deers] Jumlah total Deers yang dimuat: 100.
[02:38:08] [Dynamic Families] Jumlah total Family yang dimuat: 1.
[02:38:08] [Dynamic Locker] Jumlah total Locker yang dimuat: 11.
[02:38:08] [Dynamic Fact Craft] Jumlah total Fact Craft yang dimuat: 1.
[02:38:08] [Dynamic Armoury] Jumlah total Armoury yang dimuat: 3.
[02:38:08] [Dynamic Faction Vault] Jumlah total Faction Vault yang dimuat: 11.
[02:38:08] [Dynamic Faction Garage] Jumlah total Faction Garage yang dimuat: 11.
[02:38:08] [Dynamic Roadsigns] Total dynamic road sign loaded: 747.
[02:38:08] [Dynamic Speed Cam] Total dynamic speed cam loaded: 127.
[02:38:08] [Dynamic Graffities] Total Graffiti Tags loaded: 3.
[02:38:08] [Dynamic Blackjack] Jumlah total Blackjack Table yang dimuat: 18.
[02:38:08] [Stuffs] Server Stuff berhasil dimuat...
[02:38:08] [Farm Plants] Jumlah total farm plants yang dimuat: 1.
[02:38:33]  -------------------------------------------
[02:38:33]            SampVoice unloading...           
[02:38:33]  -------------------------------------------
[02:38:33] [dbg:raknet:free] : module releasing...
[02:38:33] [dbg:raknet:free] : module released
[02:38:33] [sv:dbg:network:free] : module releasing...
[02:38:33] [sv:dbg:network:free] : module released

----------
Loaded log file: "server_log.txt".
----------

SA-MP Dedicated Server
----------------------
v0.3.7-R2, (C)2005-2015 SA-MP Team

[02:40:20] language = ""  (string)
[02:40:20] 
[02:40:20] Server Plugins
[02:40:20] --------------
[02:40:20]  Loading plugin: crashdetect
[02:40:20]   CrashDetect plugin 4.21
[02:40:20]   Loaded.
[02:40:20]  Loading plugin: sampvoice
[02:40:20] [sv:dbg:network:init] : module initializing...
[02:40:20] [dbg:raknet:init] : module initializing...
[02:40:20] [dbg:raknet:init] : module initialized
[02:40:20] [sv:dbg:network:init] : module initialized
[02:40:20] [sv:dbg:main:Load] : creating 2 work threads...
[02:40:20]  -------------------------------------------    
[02:40:20]    ___                __   __    _              
[02:40:20]   / __| __ _ _ __  _ _\ \ / /__ (_) __ ___    
[02:40:20]   \__ \/ _` | '  \| '_ \   / _ \| |/ _/ -_)
[02:40:20]   |___/\__,_|_|_|_| .__/\_/\___/|_|\__\___|
[02:40:20]                   |_|                           
[02:40:20]  -------------------------------------------    
[02:40:20]            SampVoice by MOR loaded              
[02:40:20]         Compiled By SAMPINDO Community          
[02:40:21]  -------------------------------------------    
[02:40:21]   Loaded.
[02:40:21]  Loading plugin: KeyListener
[02:40:21] KeyListener v1.1.2 by MOR loaded
[02:40:21]   Loaded.
[02:40:21]  Loading plugin: SKY
[02:40:21] 
[02:40:21]  ===============================
[02:40:21]    
[02:40:22]    < SKY - 2.3.0 >
[02:40:22]    
[02:40:22]    (c) 2008 - Present | YSF Maintainers
[02:40:22]    (c) 2015 | Oscar "Slice" Broman
[02:40:23]    
[02:40:23]    Server Version: 0.3.7 R2
[02:40:23]    Operating System: Windows
[02:40:24]    Built on: Jan 21 2025 at 03:38:30
[02:40:24]    
[02:40:24]  ===============================
[02:40:24] 
[02:40:25]   Loaded.
[02:40:25]  Loading plugin: mysql
[02:40:25]  >> plugin.mysql: R41-4 successfully loaded.
[02:40:26]   Loaded.
[02:40:26]  Loading plugin: streamer
[02:40:26] 

*** Streamer Plugin v2.9.5 by Incognito loaded ***

[02:40:26]   Loaded.
[02:40:27]  Loading plugin: sscanf
[02:40:27] 
[02:40:27]  ===============================
[02:40:28] 
[02:40:28]       sscanf plugin loaded.     
[02:40:28] 
[02:40:28]          Version: 2.13.8
[02:40:29] 
[02:40:29]    (c) 2022 Alex "Y_Less" Cole  
[02:40:29] 
[02:40:29]  ===============================
[02:40:30] 
[02:40:30]   Loaded.
[02:40:30]  Loading plugin: samp_bcrypt
[02:40:30] [SampBcrypt] [info]: Version: 0.3.4
[02:40:30]   Loaded.
[02:40:31]  Loading plugin: textdraw-streamer
[02:40:31] 
[02:40:31]  ===============================
[02:40:32]                                 
[02:40:32]     Textdraw Streamer Yuklendi  
[02:40:32]                                 
[02:40:32]           Surum: v1.6.2            
[02:40:32]                                 
[02:40:33]     Developer: Burak (NexoR)    
[02:40:33]                                 
[02:40:33]  ===============================
[02:40:33] 
[02:40:34]   Loaded.
[02:40:34]  Loaded 9 plugins.

[02:40:35] 
[02:40:35] Filterscripts
[02:40:36] ---------------
[02:40:36]   Loading filterscript 'mapping.amx'...
[02:40:36] [sv:dbg:main:AmxLoad] : net game pointer (value:022A9FF8) received
[02:40:36] [sv:dbg:network:bind] : voice server running on port 12148
[02:40:36]  
[02:40:36]  
[02:40:36]          ==============================================================
[02:40:36]          |                                                            |
[02:40:36]          |                                                            |
[02:40:36]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[02:40:36]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[02:40:36]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[02:40:36]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[02:40:36]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[02:40:36]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[02:40:36]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[02:40:36]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[02:40:36]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[02:40:36]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[02:40:36]          |           Y:::::Y                    S:::::S   I::::I      |
[02:40:36]          |           Y:::::Y                    S:::::S   I::::I      |
[02:40:36]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[02:40:36]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[02:40:36]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[02:40:36]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[02:40:36]          |                                                            |
[02:40:36]          |                                                            |
[02:40:36]          |                      (c) 2021 MPL v1.1                     |
[02:40:36]          |            Alex "Y_Less" Cole and contributors.            |
[02:40:36]          |                                                            |
[02:40:36]          |                                                            |
[02:40:36]          ==============================================================
[02:40:36]  
[02:40:36]  
[02:40:36]  ========================================== 
[02:40:36]  |                                        | 
[02:40:36]  |   Generating code, this may take a     | 
[02:40:36]  |  little bit of time.  Note that this   | 
[02:40:36]  |  code generation works best with the   | 
[02:40:36]  |     JIT plugin, which you are not      | 
[02:40:36]  |     currently using.  Get it here:     | 
[02:40:36]  |                                        | 
[02:40:36]  |       https://git.io/jit-plugin        | 
[02:40:36]  |                                        | 
[02:40:36]  |             Please wait...             | 
[02:40:36]  |                                        | 
[02:40:36]  |             Done in 0ms!               | 
[02:40:36]  |                                        | 
[02:40:36]  ========================================== 
[02:40:36]  |                                        | 
[02:40:36]  | Server:      0.3.7-R2 (W)              | 
[02:40:36]  | Started:     04 Jun 2025 - 02:40:36    | 
[02:40:36]  |                                        | 
[02:40:36]  | Compiler:    3.10.10 (Windows)         | 
[02:40:36]  | Includes:    0.3DL                     | 
[02:40:36]  | Codepage:    <none>                    | 
[02:40:36]  | Built:       02 Feb 2025 - 14:42:04    | 
[02:40:36]  |                                        | 
[02:40:36]  | YSI:         v05.10.0006               | 
[02:40:36]  | Master:      -1                        | 
[02:40:36]  |                                        | 
[02:40:36]  | JIT:         <none>                    | 
[02:40:36]  | Crashdetect: <found>                   | 
[02:40:36]  |                                        | 
[02:40:36]  ========================================== 
[02:40:36]  
[02:40:36]  
[02:40:37]   Loading filterscript 'testvoice.amx'...
[02:40:37] -------------------- [Arivena Voice Chat System] --------------------
===>> Berhasil dimuat dengan baik!
===>> by: mhyunata (M Wahyu Dinarta)
===>> dipersembahkan untuk Arivena
-------------------------------------------------------------------
[02:40:37]   Loading filterscript 'hotkeys.amx'...
[02:40:37] -------------------- [Arivena Hotkeys System] --------------------
===>> Berhasil dimuat dengan baik!
===>> by: mhyunata (M Wahyu Dinarta)
===>> dipersembahkan untuk Arivena
-------------------------------------------------------------------
[02:40:37]   Loaded 3 filterscripts.

[02:40:38]  
[02:40:38]  
[02:40:38]          ==============================================================
[02:40:38]          |                                                            |
[02:40:38]          |                                                            |
[02:40:38]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[02:40:38]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[02:40:38]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[02:40:38]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[02:40:38]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[02:40:38]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[02:40:38]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[02:40:38]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[02:40:38]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[02:40:38]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[02:40:38]          |           Y:::::Y                    S:::::S   I::::I      |
[02:40:38]          |           Y:::::Y                    S:::::S   I::::I      |
[02:40:38]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[02:40:38]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[02:40:38]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[02:40:38]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[02:40:38]          |                                                            |
[02:40:38]          |                                                            |
[02:40:38]          |                      (c) 2021 MPL v1.1                     |
[02:40:38]          |            Alex "Y_Less" Cole and contributors.            |
[02:40:38]          |                                                            |
[02:40:38]          |                                                            |
[02:40:38]          ==============================================================
[02:40:38]  
[02:40:38]  
[02:40:38]  ========================================== 
[02:40:38]  |                                        | 
[02:40:38]  |   Generating code, this may take a     | 
[02:40:38]  |  little bit of time.  Note that this   | 
[02:40:38]  |  code generation works best with the   | 
[02:40:38]  |     JIT plugin, which you are not      | 
[02:40:38]  |     currently using.  Get it here:     | 
[02:40:38]  |                                        | 
[02:40:38]  |       https://git.io/jit-plugin        | 
[02:40:38]  |                                        | 
[02:40:38]  |             Please wait...             | 
[02:40:40]  |                                        | 
[02:40:40]  |             Done in 2794ms!            | 
[02:40:40]  |                                        | 
[02:40:40]  ========================================== 
[02:40:40]   Filterscript '../scriptfiles/callbackfix.amx' loaded.
[02:40:40] *** YSI Info: Script ID: 1
[02:40:40]  |                                        | 
[02:40:40]  | Server:      0.3.7-R2 (W)              | 
[02:40:40]  | Started:     04 Jun 2025 - 02:40:38    | 
[02:40:40]  |                                        | 
[02:40:40]  | Compiler:    3.10.08 (Windows)         | 
[02:40:40]  | Includes:    0.3DL                     | 
[02:40:40]  | Codepage:    <none>                    | 
[02:40:40]  | Built:       04 Jun 2025 - 02:37:04    | 
[02:40:40]  |                                        | 
[02:40:40]  | YSI:         v05.10.0006               | 
[02:40:40]  | Master:      1                         | 
[02:40:40]  |                                        | 
[02:40:40]  | JIT:         <none>                    | 
[02:40:40]  | Crashdetect: <found>                   | 
[02:40:40]  |                                        | 
[02:40:40]  ========================================== 
[02:40:40]  
[02:40:41]  
[02:40:41] [ADM] Info: Load EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[02:40:41] [MySQL] Koneksi ke database berhasil!
[02:40:41] [Anti-DDoS SA-MP] Telah berhasil dimuat dengan lancar.
[02:40:41] |----------------------------------|
[02:40:41] | Arivena Premiere | A4-RC-Version |
[02:40:41] |----------------------------------|
[02:40:41] Number of vehicle models: 4
[02:40:41] [Dynamic Doors] Jumlah total Doors yang dimuat: 43.
[02:40:41] [Dynamic Garbages] Jumlah total Garbages Bin yang dimuat: 497.
[02:40:41] [Dynamic Shops] Jumlah total Shops yang dimuat: 43.
[02:40:41] [Dynamic Fivem Labels] The total number of Fivem Labels loaded: 1.
[02:40:41] [Dynamic Public Garages] Jumlah total Public Garages yang dimuat: 33.
[02:40:41] [Dynamic Rentals] Total number of loaded Rentals: 6.
[02:40:41] [Dynamic Actors] Jumlah total Actors yang dimuat: 99.
[02:40:41] [Dynamic ATM] Jumlah total ATM yang dimuat: 55.
[02:40:41] [Dynamic Bank Point] Jumlah total bank point yang dimuat: 8.
[02:40:41] [Dynamic Vending] Total dynamic vending loaded: 50.
[02:40:41] [Dynamic Robberies] Jumlah total Robberies yang dimuat: 21.
[02:40:41] [Dynamic Map Icons] Jumlah total map icons yang dimuat: 49.
[02:40:41] [Dynamic Rusun] Total number of rusun loaded: 72.
[02:40:41] [Dynamic Gudang] Total number of gudang loaded: 3.
[02:40:41] [Dynamic Basement] Jumlah total basement yang dimuat: 1.
[02:40:41] [Dynamic Buttons] Jumlah total buttons yang dimuat: 22.
[02:40:41] [Dynamic Kanabis] Jumlah total Kanabis yang dimuat: 23.
[02:40:41] [Dynamic Deers] Jumlah total Deers yang dimuat: 100.
[02:40:41] [Dynamic Families] Jumlah total Family yang dimuat: 1.
[02:40:41] [Dynamic Locker] Jumlah total Locker yang dimuat: 11.
[02:40:41] [Dynamic Fact Craft] Jumlah total Fact Craft yang dimuat: 1.
[02:40:41] [Dynamic Armoury] Jumlah total Armoury yang dimuat: 3.
[02:40:41] [Dynamic Faction Vault] Jumlah total Faction Vault yang dimuat: 11.
[02:40:41] [Dynamic Faction Garage] Jumlah total Faction Garage yang dimuat: 11.
[02:40:42] [Dynamic Roadsigns] Total dynamic road sign loaded: 747.
[02:40:42] [Dynamic Speed Cam] Total dynamic speed cam loaded: 127.
[02:40:42] [Dynamic Graffities] Total Graffiti Tags loaded: 3.
[02:40:42] [Dynamic Blackjack] Jumlah total Blackjack Table yang dimuat: 18.
[02:40:42] [Stuffs] Server Stuff berhasil dimuat...
[02:40:42] [Farm Plants] Jumlah total farm plants yang dimuat: 1.
[02:41:36]  -------------------------------------------
[02:41:36]            SampVoice unloading...           
[02:41:36]  -------------------------------------------
[02:41:36] [dbg:raknet:free] : module releasing...
[02:41:36] [dbg:raknet:free] : module released
[02:41:36] [sv:dbg:network:free] : module releasing...
[02:41:36] [sv:dbg:network:free] : module released

----------
Loaded log file: "server_log.txt".
----------

SA-MP Dedicated Server
----------------------
v0.3.7-R2, (C)2005-2015 SA-MP Team

[02:45:34] language = ""  (string)
[02:45:34] 
[02:45:34] Server Plugins
[02:45:34] --------------
[02:45:34]  Loading plugin: crashdetect
[02:45:34]   CrashDetect plugin 4.21
[02:45:34]   Loaded.
[02:45:34]  Loading plugin: sampvoice
[02:45:34] [sv:dbg:network:init] : module initializing...
[02:45:34] [dbg:raknet:init] : module initializing...
[02:45:34] [dbg:raknet:init] : module initialized
[02:45:34] [sv:dbg:network:init] : module initialized
[02:45:34] [sv:dbg:main:Load] : creating 2 work threads...
[02:45:34]  -------------------------------------------    
[02:45:34]    ___                __   __    _              
[02:45:34]   / __| __ _ _ __  _ _\ \ / /__ (_) __ ___    
[02:45:34]   \__ \/ _` | '  \| '_ \   / _ \| |/ _/ -_)
[02:45:34]   |___/\__,_|_|_|_| .__/\_/\___/|_|\__\___|
[02:45:34]                   |_|                           
[02:45:34]  -------------------------------------------    
[02:45:34]            SampVoice by MOR loaded              
[02:45:35]         Compiled By SAMPINDO Community          
[02:45:35]  -------------------------------------------    
[02:45:35]   Loaded.
[02:45:35]  Loading plugin: KeyListener
[02:45:35] KeyListener v1.1.2 by MOR loaded
[02:45:35]   Loaded.
[02:45:35]  Loading plugin: SKY
[02:45:35] 
[02:45:36]  ===============================
[02:45:36]    
[02:45:36]    < SKY - 2.3.0 >
[02:45:36]    
[02:45:36]    (c) 2008 - Present | YSF Maintainers
[02:45:36]    (c) 2015 | Oscar "Slice" Broman
[02:45:36]    
[02:45:37]    Server Version: 0.3.7 R2
[02:45:37]    Operating System: Windows
[02:45:37]    Built on: Jan 21 2025 at 03:38:30
[02:45:37]    
[02:45:37]  ===============================
[02:45:37] 
[02:45:37]   Loaded.
[02:45:37]  Loading plugin: mysql
[02:45:38]  >> plugin.mysql: R41-4 successfully loaded.
[02:45:38]   Loaded.
[02:45:38]  Loading plugin: streamer
[02:45:38] 

*** Streamer Plugin v2.9.5 by Incognito loaded ***

[02:45:38]   Loaded.
[02:45:38]  Loading plugin: sscanf
[02:45:38] 
[02:45:38]  ===============================
[02:45:39] 
[02:45:39]       sscanf plugin loaded.     
[02:45:39] 
[02:45:39]          Version: 2.13.8
[02:45:39] 
[02:45:39]    (c) 2022 Alex "Y_Less" Cole  
[02:45:39] 
[02:45:40]  ===============================
[02:45:40] 
[02:45:40]   Loaded.
[02:45:40]  Loading plugin: samp_bcrypt
[02:45:40] [SampBcrypt] [info]: Version: 0.3.4
[02:45:40]   Loaded.
[02:45:40]  Loading plugin: textdraw-streamer
[02:45:40] 
[02:45:40]  ===============================
[02:45:41]                                 
[02:45:41]     Textdraw Streamer Yuklendi  
[02:45:41]                                 
[02:45:41]           Surum: v1.6.2            
[02:45:41]                                 
[02:45:41]     Developer: Burak (NexoR)    
[02:45:41]                                 
[02:45:41]  ===============================
[02:45:42] 
[02:45:42]   Loaded.
[02:45:42]  Loaded 9 plugins.

[02:45:43] 
[02:45:43] Filterscripts
[02:45:43] ---------------
[02:45:43]   Loading filterscript 'mapping.amx'...
[02:45:43] [sv:dbg:main:AmxLoad] : net game pointer (value:022C94B8) received
[02:45:43] [sv:dbg:network:bind] : voice server running on port 12148
[02:45:43]  
[02:45:43]  
[02:45:43]          ==============================================================
[02:45:43]          |                                                            |
[02:45:43]          |                                                            |
[02:45:43]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[02:45:43]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[02:45:43]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[02:45:43]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[02:45:43]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[02:45:43]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[02:45:43]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[02:45:43]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[02:45:43]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[02:45:43]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[02:45:43]          |           Y:::::Y                    S:::::S   I::::I      |
[02:45:43]          |           Y:::::Y                    S:::::S   I::::I      |
[02:45:43]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[02:45:43]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[02:45:43]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[02:45:43]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[02:45:43]          |                                                            |
[02:45:43]          |                                                            |
[02:45:43]          |                      (c) 2021 MPL v1.1                     |
[02:45:43]          |            Alex "Y_Less" Cole and contributors.            |
[02:45:43]          |                                                            |
[02:45:43]          |                                                            |
[02:45:43]          ==============================================================
[02:45:43]  
[02:45:43]  
[02:45:43]  ========================================== 
[02:45:43]  |                                        | 
[02:45:43]  |   Generating code, this may take a     | 
[02:45:43]  |  little bit of time.  Note that this   | 
[02:45:43]  |  code generation works best with the   | 
[02:45:43]  |     JIT plugin, which you are not      | 
[02:45:43]  |     currently using.  Get it here:     | 
[02:45:43]  |                                        | 
[02:45:43]  |       https://git.io/jit-plugin        | 
[02:45:43]  |                                        | 
[02:45:43]  |             Please wait...             | 
[02:45:43]  |                                        | 
[02:45:43]  |             Done in 0ms!               | 
[02:45:43]  |                                        | 
[02:45:43]  ========================================== 
[02:45:43]  |                                        | 
[02:45:43]  | Server:      0.3.7-R2 (W)              | 
[02:45:43]  | Started:     04 Jun 2025 - 02:45:43    | 
[02:45:43]  |                                        | 
[02:45:43]  | Compiler:    3.10.10 (Windows)         | 
[02:45:43]  | Includes:    0.3DL                     | 
[02:45:43]  | Codepage:    <none>                    | 
[02:45:43]  | Built:       02 Feb 2025 - 14:42:04    | 
[02:45:43]  |                                        | 
[02:45:43]  | YSI:         v05.10.0006               | 
[02:45:43]  | Master:      -1                        | 
[02:45:43]  |                                        | 
[02:45:43]  | JIT:         <none>                    | 
[02:45:43]  | Crashdetect: <found>                   | 
[02:45:43]  |                                        | 
[02:45:43]  ========================================== 
[02:45:43]  
[02:45:43]  
[02:45:44]   Loading filterscript 'testvoice.amx'...
[02:45:44] -------------------- [Arivena Voice Chat System] --------------------
===>> Berhasil dimuat dengan baik!
===>> by: mhyunata (M Wahyu Dinarta)
===>> dipersembahkan untuk Arivena
-------------------------------------------------------------------
[02:45:44]   Loading filterscript 'hotkeys.amx'...
[02:45:44] -------------------- [Arivena Hotkeys System] --------------------
===>> Berhasil dimuat dengan baik!
===>> by: mhyunata (M Wahyu Dinarta)
===>> dipersembahkan untuk Arivena
-------------------------------------------------------------------
[02:45:44]   Loaded 3 filterscripts.

[02:45:45]  
[02:45:45]  
[02:45:45]          ==============================================================
[02:45:45]          |                                                            |
[02:45:45]          |                                                            |
[02:45:45]          |    YYYYYYY       YYYYYYY    SSSSSSSSSSSSSSS  IIIIIIIIII    |
[02:45:45]          |    Y:::::Y       Y:::::Y  SS:::::::::::::::S I::::::::I    |
[02:45:45]          |    Y:::::Y       Y:::::Y S:::::SSSSSS::::::S I::::::::I    |
[02:45:45]          |    Y::::::Y     Y::::::Y S:::::S     SSSSSSS II::::::II    |
[02:45:45]          |    YYY:::::Y   Y:::::YYY S:::::S               I::::I      |
[02:45:45]          |       Y:::::Y Y:::::Y    S:::::S               I::::I      |
[02:45:45]          |        Y:::::Y:::::Y      S::::SSSS            I::::I      |
[02:45:45]          |         Y:::::::::Y        SS::::::SSSSS       I::::I      |
[02:45:45]          |          Y:::::::Y           SSS::::::::SS     I::::I      |
[02:45:45]          |           Y:::::Y               SSSSSS::::S    I::::I      |
[02:45:45]          |           Y:::::Y                    S:::::S   I::::I      |
[02:45:45]          |           Y:::::Y                    S:::::S   I::::I      |
[02:45:45]          |           Y:::::Y        SSSSSSS     S:::::S II::::::II    |
[02:45:45]          |        YYYY:::::YYYY     S::::::SSSSSS:::::S I::::::::I    |
[02:45:45]          |        Y:::::::::::Y     S:::::::::::::::SS  I::::::::I    |
[02:45:45]          |        YYYYYYYYYYYYY      SSSSSSSSSSSSSSS    IIIIIIIIII    |
[02:45:45]          |                                                            |
[02:45:45]          |                                                            |
[02:45:45]          |                      (c) 2021 MPL v1.1                     |
[02:45:45]          |            Alex "Y_Less" Cole and contributors.            |
[02:45:45]          |                                                            |
[02:45:45]          |                                                            |
[02:45:45]          ==============================================================
[02:45:45]  
[02:45:45]  
[02:45:45]  ========================================== 
[02:45:45]  |                                        | 
[02:45:45]  |   Generating code, this may take a     | 
[02:45:45]  |  little bit of time.  Note that this   | 
[02:45:45]  |  code generation works best with the   | 
[02:45:45]  |     JIT plugin, which you are not      | 
[02:45:45]  |     currently using.  Get it here:     | 
[02:45:45]  |                                        | 
[02:45:45]  |       https://git.io/jit-plugin        | 
[02:45:45]  |                                        | 
[02:45:45]  |             Please wait...             | 
[02:45:48]  |                                        | 
[02:45:48]  |             Done in 3072ms!            | 
[02:45:48]  |                                        | 
[02:45:48]  ========================================== 
[02:45:48]   Filterscript '../scriptfiles/callbackfix.amx' loaded.
[02:45:48] *** YSI Info: Script ID: 1
[02:45:48]  |                                        | 
[02:45:48]  | Server:      0.3.7-R2 (W)              | 
[02:45:48]  | Started:     04 Jun 2025 - 02:45:45    | 
[02:45:48]  |                                        | 
[02:45:48]  | Compiler:    3.10.08 (Windows)         | 
[02:45:48]  | Includes:    0.3DL                     | 
[02:45:48]  | Codepage:    <none>                    | 
[02:45:48]  | Built:       04 Jun 2025 - 02:37:04    | 
[02:45:48]  |                                        | 
[02:45:48]  | YSI:         v05.10.0006               | 
[02:45:48]  | Master:      1                         | 
[02:45:48]  |                                        | 
[02:45:48]  | JIT:         <none>                    | 
[02:45:48]  | Crashdetect: <found>                   | 
[02:45:48]  |                                        | 
[02:45:48]  ========================================== 
[02:45:48]  
[02:45:48]  
[02:45:48] [ADM] Info: Load EVF v3.3.3 for SA:MP 0.3 DL by Abyss Morgan
[02:45:48] [MySQL] Koneksi ke database berhasil!
[02:45:48] [Anti-DDoS SA-MP] Telah berhasil dimuat dengan lancar.
[02:45:48] |----------------------------------|
[02:45:48] | Arivena Premiere | A4-RC-Version |
[02:45:48] |----------------------------------|
[02:45:48] Number of vehicle models: 4
[02:45:48] [Dynamic Doors] Jumlah total Doors yang dimuat: 43.
[02:45:48] [Dynamic Garbages] Jumlah total Garbages Bin yang dimuat: 497.
[02:45:48] [Dynamic Shops] Jumlah total Shops yang dimuat: 43.
[02:45:48] [Dynamic Fivem Labels] The total number of Fivem Labels loaded: 1.
[02:45:48] [Dynamic Public Garages] Jumlah total Public Garages yang dimuat: 33.
[02:45:48] [Dynamic Rentals] Total number of loaded Rentals: 6.
[02:45:49] [Dynamic Actors] Jumlah total Actors yang dimuat: 99.
[02:45:49] [Dynamic ATM] Jumlah total ATM yang dimuat: 55.
[02:45:49] [Dynamic Bank Point] Jumlah total bank point yang dimuat: 8.
[02:45:49] [Dynamic Vending] Total dynamic vending loaded: 50.
[02:45:49] [Dynamic Robberies] Jumlah total Robberies yang dimuat: 21.
[02:45:49] [Dynamic Map Icons] Jumlah total map icons yang dimuat: 49.
[02:45:49] [Dynamic Rusun] Total number of rusun loaded: 72.
[02:45:49] [Dynamic Gudang] Total number of gudang loaded: 3.
[02:45:49] [Dynamic Basement] Jumlah total basement yang dimuat: 1.
[02:45:49] [Dynamic Buttons] Jumlah total buttons yang dimuat: 22.
[02:45:49] [Dynamic Kanabis] Jumlah total Kanabis yang dimuat: 23.
[02:45:49] [Dynamic Deers] Jumlah total Deers yang dimuat: 100.
[02:45:49] [Dynamic Families] Jumlah total Family yang dimuat: 1.
[02:45:49] [Dynamic Locker] Jumlah total Locker yang dimuat: 11.
[02:45:49] [Dynamic Fact Craft] Jumlah total Fact Craft yang dimuat: 1.
[02:45:49] [Dynamic Armoury] Jumlah total Armoury yang dimuat: 3.
[02:45:49] [Dynamic Faction Vault] Jumlah total Faction Vault yang dimuat: 11.
[02:45:49] [Dynamic Faction Garage] Jumlah total Faction Garage yang dimuat: 11.
[02:45:49] [Dynamic Roadsigns] Total dynamic road sign loaded: 747.
[02:45:49] [Dynamic Speed Cam] Total dynamic speed cam loaded: 127.
[02:45:49] [Dynamic Graffities] Total Graffiti Tags loaded: 3.
[02:45:49] [Dynamic Blackjack] Jumlah total Blackjack Table yang dimuat: 18.
[02:45:49] [Stuffs] Server Stuff berhasil dimuat...
[02:45:49] [Farm Plants] Jumlah total farm plants yang dimuat: 1.
[02:46:15] [connection] incoming connection: **************:25872 id: 0
[02:46:15] [connection] incoming connection: **************:25874 id: 0
[02:46:15] [join] Akbar has joined the server (0:**************)
[02:46:17] [part] Akbar has left the server (0:2)
[02:47:30] [connection] incoming connection: **************:26019 id: 0
[02:47:30] [connection] incoming connection: **************:26032 id: 0
[02:47:30] [join] ARJUNA has joined the server (0:**************)
[02:47:32] [part] ARJUNA has left the server (0:2)
[03:23:45] [connection] incoming connection: **************:22707 id: 0
[03:23:45] [connection] incoming connection: **************:22708 id: 0
[03:23:46] [join] ARJUNA has joined the server (0:**************)
[03:23:47] [part] ARJUNA has left the server (0:2)
[05:30:37] [connection] incoming connection: **************:26053 id: 0
[05:30:37] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[05:30:37] [sv:dbg:network:connect] : player (0) assigned key (976ed131)
[05:30:37] [join] Akbar has joined the server (0:**************)
[05:30:37] [sv:dbg:network:receive] : player (0) identified (port:26058)
[05:31:05] [nick] Akbar nick changed to Surya_Akbar
[05:39:42] [nick] Surya_Akbar nick changed to Akbar
[05:39:50] [nick] Akbar nick changed to Surya_Akbar
[05:39:50] [nick] Surya_Akbar nick changed to Akbar
[05:53:38] [sv:dbg:network:connect] : disconnecting player (0) ...
[05:53:38] [part] Akbar has left the server (0:1)
[05:57:39] [connection] incoming connection: **************:36387 id: 0
[05:57:40] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[05:57:40] [sv:dbg:network:connect] : player (0) assigned key (f0403503)
[05:57:40] [join] Akbar has joined the server (0:**************)
[05:57:40] [sv:dbg:network:receive] : player (0) identified (port:36400)
[05:58:06] [nick] Akbar nick changed to Surya_Akbar
[05:58:06] [Player Inventory] Total number of inventory items loaded for Akbar [DBID: 4]: 4.
[05:58:06] [Player Smartphone Loaded] Akbar [DBID: 4]
[05:58:06] [Player Warns] Total number of warnings loaded for Akbar [DBID: 4]: 2.
[05:59:08] [sv:dbg:network:connect] : disconnecting player (0) ...
[05:59:08] [part] Surya_Akbar has left the server (0:1)
[06:01:41] [connection] incoming connection: **************:36391 id: 0
[06:01:42] [sv:dbg:network:connect] : connecting player (0) with address (**************) ...
[06:01:42] [sv:dbg:network:connect] : player (0) assigned key (f27b657)
[06:01:42] [join] Akbar has joined the server (0:**************)
[06:01:42] [sv:dbg:network:receive] : player (0) identified (port:36392)
[06:01:52] [nick] Akbar nick changed to Surya_Akbar
[06:01:52] [Player Inventory] Total number of inventory items loaded for Akbar [DBID: 4]: 4.
[06:01:52] [Player Smartphone Loaded] Akbar [DBID: 4]
[06:01:52] [Player Warns] Total number of warnings loaded for Akbar [DBID: 4]: 2.
[06:01:52] [Player Weapons] Total number of weapons loaded for Akbar [DBID: 4]: 1.
[06:02:08] [nick] Surya_Akbar nick changed to Akbar
[06:02:49] [nick] Akbar nick changed to Surya_Akbar
[06:02:52] [nick] Surya_Akbar nick changed to Akbar
[06:03:14] [sv:dbg:network:connect] : disconnecting player (0) ...
[06:03:14] [part] Akbar has left the server (0:1)
[04:19:39]  -------------------------------------------
[04:19:39]            SampVoice unloading...           
[04:19:39]  -------------------------------------------
[04:19:39] [dbg:raknet:free] : module releasing...
[04:19:39] [dbg:raknet:free] : module released
[04:19:39] [sv:dbg:network:free] : module releasing...
[04:19:39] [sv:dbg:network:free] : module released
